import React from "react";
import ReactDOM from "react-dom/client";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
  Badge,
  Button,
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  Label,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Switch,
  ThemeProvider,
  useTheme,
} from "@nui/ui";

type ThemeMode = "light" | "dark" | "auto";

function App() {
  const {
    config: { mode, variant },
    isDark,
    availableVariants,
    setMode,
    setVariant,
    toggleMode,
    cycleVariant,
  } = useTheme();

  return (
    <div className="min-h-screen p-8 bg-background text-foreground">
      <div className="max-w-4xl mx-auto space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <h1 className="text-3xl font-bold">Theme System Demo</h1>
            <Badge variant="success">Live</Badge>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="outline">Admin</Badge>
            <Avatar size="lg">
              <AvatarImage
                src="https://github.com/shadcn.png"
                alt="User Avatar"
              />
              <AvatarFallback>CN</AvatarFallback>
            </Avatar>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="p-4 bg-card border rounded-lg space-y-2 text-sm">
            <h2 className="text-xl font-semibold mb-3">Current Theme</h2>
            <div className="flex items-center gap-2">
              Mode:{" "}
              <Badge variant={isDark ? "secondary" : "default"}>{mode}</Badge>
            </div>
            <div className="flex items-center gap-2">
              Variant: <Badge variant="outline">{variant}</Badge>
            </div>
            <div className="flex items-center gap-2">
              Is Dark:{" "}
              <Badge variant={isDark ? "info" : "warning"}>
                {isDark.toString()}
              </Badge>
            </div>
            <div>
              Available: <code>{availableVariants.join(", ")}</code>
            </div>
          </div>

          <div className="p-4 bg-card border rounded-lg space-y-3">
            <h2 className="text-xl font-semibold mb-3">Theme Controls</h2>

            <div className="flex items-center justify-between">
              <Label htmlFor="dark-mode-switch">Dark Mode</Label>
              <Switch
                id="dark-mode-switch"
                checked={isDark}
                onCheckedChange={toggleMode}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="variant-select">Theme Variant</Label>
              <Select
                value={variant}
                onValueChange={(value) => setVariant(value as string)}
              >
                <SelectTrigger id="variant-select" className="w-full">
                  <SelectValue placeholder="Select variant" />
                </SelectTrigger>
                <SelectContent>
                  {availableVariants.map((v) => (
                    <SelectItem key={v} value={v}>
                      {v.charAt(0).toUpperCase() + v.slice(1)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <Button onClick={toggleMode} className="w-full">
              Toggle Mode ({mode})
            </Button>
            <Button
              onClick={() => cycleVariant(availableVariants)}
              className="w-full"
              variant="secondary"
            >
              Cycle Variant ({variant})
            </Button>

            <div className="grid grid-cols-3 gap-2">
              {(["light", "dark", "auto"] as ThemeMode[]).map((m) => (
                <Button
                  key={m}
                  onClick={() => setMode(m)}
                  variant={mode === m ? "default" : "outline"}
                  size="sm"
                >
                  {m}
                </Button>
              ))}
            </div>

            <div className="grid grid-cols-2 gap-2">
              {availableVariants.map((v) => (
                <Button
                  key={v}
                  onClick={() => setVariant(v)}
                  variant={variant === v ? "default" : "outline"}
                  size="sm"
                >
                  {v}
                </Button>
              ))}
            </div>
          </div>
        </div>

        <Card className="max-w-md mx-auto">
          <CardHeader>
            <CardTitle>Component Showcase</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <span>Avatar with Badge</span>
              <div className="flex items-center gap-2">
                <Badge variant="success">Online</Badge>
                <Avatar size="sm">
                  <AvatarImage
                    src="https://github.com/vercel.png"
                    alt="Vercel"
                  />
                  <AvatarFallback>V</AvatarFallback>
                </Avatar>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <span>Notifications</span>
              <Switch defaultChecked />
            </div>

            <div className="space-y-2">
              <Label>Priority Level</Label>
              <Select defaultValue="medium">
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="low">Low</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                  <SelectItem value="urgent">Urgent</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

ReactDOM.createRoot(document.getElementById("root")!).render(
  <React.StrictMode>
    <ThemeProvider>
      <App />
    </ThemeProvider>
  </React.StrictMode>,
);
